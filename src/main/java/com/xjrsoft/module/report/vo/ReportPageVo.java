package com.xjrsoft.module.report.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: tzx
 * @Date: 2022/5/23 15:56
 */
@Data
public class ReportPageVo {

    @ApiModelProperty("主键值")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("排序码")
    private String remark;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("类型,0-ureport,1-葡萄城数据")
    private Integer dataType;

    @ApiModelProperty("创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifyDate;

}
