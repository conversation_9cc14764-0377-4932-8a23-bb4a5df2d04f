<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.mobile.mapper.ZyMobileViewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.mobile.entity.ZyMobileView">
        <result column="ID" property="id" />
        <result column="SCHEME" property="scheme" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="REMARKS" property="remarks" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="NAME" property="name" />
        <result column="SCHEMEINFO_ID" property="schemeinfoId" />
        <result column="IS_USED" property="isUsed" />
        <result column="IS_TODO_VIEW" property="isTodoView" />
        <result column="EXT_PARAM" property="extParam" />
        <result column="BASIC_ID" property="basicId" />
        <result column="IS_SURVEY" property="isSurvey" />
        <result column="SURVEY_TYPE" property="surveyType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SCHEME, CREATE_DATE, CREATE_BY, UPDATE_DATE, UPDATE_BY, REMARKS, DEL_FLAG, NAME, SCHEMEINFO_ID, IS_USED, IS_TODO_VIEW, EXT_PARAM, BASIC_ID, IS_SURVEY, SURVEY_TYPE
    </sql>

</mapper>
