<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.mobile.mapper.ZyMobileBasicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.mobile.entity.ZyMobileBasic">
        <result column="ID" property="id" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="REMARKS" property="remarks" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="NAME" property="name" />
        <result column="LABEL" property="label" />
        <result column="CATEGORY" property="category" />
        <result column="ICON" property="icon" />
        <result column="COLOR" property="color" />
        <result column="SCHEMEINFO_ID" property="schemeinfoId" />
        <result column="USERS" property="users" />
        <result column="OFFICES" property="offices" />
        <result column="ROLES" property="roles" />
        <result column="TYPE" property="type" />
        <result column="DEL_URL" property="delUrl" />
        <result column="JUMP_TYPE" property="jumpType" />
        <result column="SORT" property="sort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CREATE_DATE, CREATE_BY, UPDATE_DATE, UPDATE_BY, REMARKS, DEL_FLAG, NAME, LABEL, CATEGORY, ICON, COLOR, SCHEMEINFO_ID, USERS, OFFICES, ROLES, TYPE, DEL_URL, JUMP_TYPE, SORT
    </sql>

</mapper>
