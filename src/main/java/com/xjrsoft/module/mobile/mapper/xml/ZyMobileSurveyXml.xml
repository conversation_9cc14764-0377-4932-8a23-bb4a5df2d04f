<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.mobile.mapper.ZyMobileSurveyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.mobile.entity.ZyMobileSurvey">
        <id column="ROW_ID" property="rowId" />
        <result column="CREATE_BY" property="createBy" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_DATE" property="updateDate" />
        <result column="REMARKS" property="remarks" />
        <result column="DESIGN_ID" property="designId" />
        <result column="CONTENT" property="content" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ROW_ID, CREATE_BY, CREATE_DATE, UPDATE_BY, UPDATE_DATE, REMARKS, DESIGN_ID, CONTENT
    </sql>

</mapper>
