<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xjrsoft.module.mobile.mapper.ZyMobileMarkedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xjrsoft.module.mobile.entity.ZyMobileMarked">
        <result column="SCHEMEINFO_ID" property="schemeinfoId" />
        <result column="BUSINESSID" property="businessid" />
        <result column="USER_ID" property="userId" />
        <result column="CREATE_BY" property="createBy" />
        <result column="CREATE_DATE" property="createDate" />
        <result column="PROC_INST_ID" property="procInstId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        SCHEMEINFO_ID, BUSINESSID, USER_ID, CREATE_BY, CREATE_DATE, PROC_INST_ID
    </sql>

</mapper>
