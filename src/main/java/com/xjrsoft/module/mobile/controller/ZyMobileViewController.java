package com.xjrsoft.module.mobile.controller;

import cn.hutool.core.lang.TypeReference;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xjrsoft.common.exception.MyException;
import com.xjrsoft.common.page.Order;
import com.xjrsoft.common.pojo.CommonResult;
import com.xjrsoft.common.utils.RedisUtil;
import com.xjrsoft.module.form.entity.FormTemplate;
import com.xjrsoft.module.form.service.IFormTemplateService;
import com.xjrsoft.module.mobile.entity.ZyMobileView;
import com.xjrsoft.module.mobile.service.IZyMobileViewService;
import com.xjrsoft.module.workflow.service.IWorkflowExecuteService;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaService;
import com.xjrsoft.module.zy.form.base.BaseController;
import com.xjrsoft.module.zy.form.pojo.entity.ZyFormSchemeinfo;
import com.xjrsoft.module.zy.form.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 单据设计器移动端视图配置表
 *
 * <AUTHOR>
 * @since 2024-09-01
 */
@Api(tags = "单据设计器移动端视图配置表")
@RestController
@RequestMapping("/zyMobileView")
@Log4j2
public class ZyMobileViewController extends BaseController<ZyMobileView, ZyMobileView, ZyMobileView, IZyMobileViewService> {

    @Autowired
    IFormTemplateService formTemplateService;
    @Autowired
    ZyFormDatasetService zyFormDatasetService;
    @Autowired
    IWorkflowExecuteService iWorkflowExecuteService;
    @Autowired
    ZyFormProcessNodeService zyFormProcessNodeService;
    @Autowired
    ZyFormEditButtonService zyFormEditButtonService;
    @Autowired
    ZyFormEditWidgetService zyFormEditWidgetService;
    @Autowired
    ZyFormEditToolService zyFormEditToolService;
    @Autowired
    IWorkflowSchemaService iWorkflowSchemaService;
    @Autowired
    ZyFormEditService zyFormEditService;
    @Autowired
    ZyFormSchemeinfoService zyFormSchemeinfoService;
    @Autowired
    private RedisUtil redisUtil;


    //保存后回调
    @Override
    public void afterSave(ZyMobileView entity, ZyMobileView entityVO) {
        updateFormJson(entity, entityVO);
    }

    @Override
    public void afterUpdate(ZyMobileView entity, ZyMobileView entityVO) {
        updateFormJson(entity, entityVO);
    }

    /**
     * 更新formJson
     */
    public void updateFormJson(ZyMobileView entity, ZyMobileView entityVO) {
        redisUtil.delete("ZyMobileView");
        try {
            String formTemplateId = entityVO.getFormTemplateId();
            FormTemplate formTemplate = null;
            if (StringUtils.isNotBlank(formTemplateId)) {
                formTemplate = formTemplateService.getById(formTemplateId);
            }
            if (formTemplate == null) {

                formTemplate = formTemplateService.getById(entityVO.getId());
            }
            if (formTemplate == null) {
                throw new MyException("未找到对应的表单模板");
            }
            entity.setScheme(formTemplate.getFormJson());
            service.updateById(entity);
            if (formTemplate != null) {
                try {
                    formTemplateService.update(new LambdaUpdateWrapper<FormTemplate>()
                            .eq(FormTemplate::getId, formTemplate.getId())
                            .set(FormTemplate::getId, entity.getId())
                            .set(FormTemplate::getFormJson, entity.getScheme()));
                } catch (Exception e) {
                    formTemplateService.update(new LambdaUpdateWrapper<FormTemplate>()
                            .eq(FormTemplate::getId, formTemplate.getId())
                            .set(FormTemplate::getFormJson, entity.getScheme()));
                }
                entity.setFormTemplateId(entity.getId());
                service.updateById(entity);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("更新formJson失败", e);
        }
    }


    @Override
    @ApiOperation("查询所有")
    @GetMapping("/list")
    public CommonResult<List<ZyMobileView>> list(ZyMobileView pageReqVO, Order order) {

        TypeReference<List<ZyMobileView>> typeReference = new TypeReference<>() {
        };
        List<ZyMobileView> zyMobileView = redisUtil.get("ZyMobileView", typeReference);
        CommonResult<List<ZyMobileView>> result;
        if (zyMobileView == null) {
            result = super.list(pageReqVO, order);
        }else {
            result = CommonResult.success(zyMobileView);
            return result;
        }

        if (result.isSuccess()) {
            List<ZyMobileView> data = result.getData();
            if (data != null) {
                data.forEach(x -> {
                    if (StringUtils.isNotBlank(x.getSchemeinfoId())) {
                        ZyFormSchemeinfo zyFormSchemeinfo = zyFormSchemeinfoService.getById(x.getSchemeinfoId());
                        if (zyFormSchemeinfo != null) {
                            x.setSchemeInfo(zyFormSchemeinfo);
                        }
                    }
                });
            }
        }
        redisUtil.set("ZyMobileView", result.getData());
        return result;
    }

}
