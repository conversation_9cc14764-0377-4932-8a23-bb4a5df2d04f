package com.xjrsoft.module.mobile.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xjrsoft.module.form.entity.FormTemplate;
import com.xjrsoft.module.form.service.IFormTemplateService;
import com.xjrsoft.module.mobile.entity.ZyMobileBasic;
import com.xjrsoft.module.mobile.service.IZyMobileBasicService;
import com.xjrsoft.module.workflow.service.IWorkflowExecuteService;
import com.xjrsoft.module.workflow.service.IWorkflowSchemaService;
import com.xjrsoft.module.zy.form.base.BaseController;
import com.xjrsoft.module.zy.form.service.*;
import io.swagger.annotations.Api;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 单据设计器移动端基础配置信息表
 * <AUTHOR>
 * @since 2024-09-01
 */
@Api(tags ="单据设计器移动端基础配置信息表")
@RestController
@RequestMapping("/zyMobileBasic")
@Log4j2
public class ZyMobileBasicController extends BaseController<ZyMobileBasic,ZyMobileBasic,ZyMobileBasic, IZyMobileBasicService> {
    @Autowired
    IFormTemplateService formTemplateService;
    @Autowired
    ZyFormDatasetService zyFormDatasetService;
    @Autowired
    IWorkflowExecuteService iWorkflowExecuteService;
    @Autowired
    ZyFormProcessNodeService zyFormProcessNodeService;
    @Autowired
    ZyFormEditButtonService zyFormEditButtonService;
    @Autowired
    ZyFormEditWidgetService zyFormEditWidgetService;
    @Autowired
    ZyFormEditToolService zyFormEditToolService;
    @Autowired
    IWorkflowSchemaService iWorkflowSchemaService;
    @Autowired
    ZyFormEditService zyFormEditService;


    //保存后回调
    @Override
    public void afterSave(ZyMobileBasic entity, ZyMobileBasic entityVO) {
        updateFormJson(entity, entityVO);
    }

    @Override
    public void afterUpdate(ZyMobileBasic entity, ZyMobileBasic entityVO) {
        updateFormJson(entity, entityVO);
    }

    /**
     * 更新formJson
     */
    public void updateFormJson(ZyMobileBasic entity, ZyMobileBasic entityVO) {
        try {
            String formTemplateId = entityVO.getFormTemplateId();
            if (formTemplateId == null) return;
            FormTemplate formTemplate = formTemplateService.getById(formTemplateId);
            if (formTemplate == null) return;
            entity.setScheme(formTemplate.getFormJson());
            service.updateById(entity);
            if (formTemplate != null) {
                formTemplateService.update(new LambdaUpdateWrapper<FormTemplate>()
                        .eq(FormTemplate::getId, formTemplate.getId())
                        .set(FormTemplate::getId, entity.getId()));
                entity.setFormTemplateId(entity.getId());
                service.updateById(entity);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("更新formJson失败", e);
        }
    }
}
