package com.xjrsoft.module.mobile.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-01
 */
@Getter
@Setter
@TableName("ZY_MOBILE_MARKED")
@ApiModel(value = "ZyMobileMarked对象", description = "")
public class ZyMobileMarked implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("SCHEMEINFO_ID")
    private String schemeinfoId;

    @TableField("BUSINESSID")
    private String businessid;

    @TableField("USER_ID")
    private String userId;

    @ApiModelProperty("创建人")
    @TableField("CREATE_BY")
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    @ApiModelProperty("流程实例")
    @TableField("PROC_INST_ID")
    private String procInstId;
}
