package com.xjrsoft.module.mobile.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-01
 */
@Getter
@Setter
@TableName("ZY_MOBILE_BASIC")
@ApiModel(value = "ZyMobileBasic对象", description = "")
public class ZyMobileBasic implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private String id;

    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_DATE")
    private LocalDateTime updateDate;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("REMARKS")
    private String remarks;

    @TableField("DEL_FLAG")
    private String delFlag;

    @TableField("NAME")
    private String name;

    @TableField("LABEL")
    private String label;

    @TableField("CATEGORY")
    private String category;

    @TableField("ICON")
    private String icon;

    @TableField("COLOR")
    private String color;

    @TableField("SCHEMEINFO_ID")
    private String schemeinfoId;

    @ApiModelProperty("用户权限")
    @TableField("USERS")
    private String users;

    @ApiModelProperty("单位权限")
    @TableField("OFFICES")
    private String offices;

    @ApiModelProperty("角色权限")
    @TableField("ROLES")
    private String roles;

    @ApiModelProperty("app按钮类型 0为默认按钮 1为拓展按钮")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty("跳转url")
    @TableField("DEL_URL")
    private String delUrl;

    @ApiModelProperty("跳转类型 0只读 1可编辑 2自定义")
    @TableField("JUMP_TYPE")
    private String jumpType;

    @ApiModelProperty("排序")
    @TableField("SORT")
    private String sort;

    @ApiModelProperty("模版")
    @TableField("FORM_TEMPLATE_ID")
    private String formTemplateId;

    @ApiModelProperty("模版")
    @TableField("SCHEME")
    private String scheme;
}
