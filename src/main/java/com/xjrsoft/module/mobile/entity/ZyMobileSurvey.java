package com.xjrsoft.module.mobile.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 问卷填写记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-01
 */
@Getter
@Setter
@TableName("ZY_MOBILE_SURVEY")
@ApiModel(value = "ZyMobileSurvey对象", description = "问卷填写记录表")
public class ZyMobileSurvey implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId("ROW_ID")
    private String rowId;

    @ApiModelProperty("创建人")
    @TableField("CREATE_BY")
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    @ApiModelProperty("修改人")
    @TableField("UPDATE_BY")
    private String updateBy;

    @ApiModelProperty("修改时间")
    @TableField("UPDATE_DATE")
    private LocalDateTime updateDate;

    @ApiModelProperty("备注")
    @TableField("REMARKS")
    private String remarks;

    @ApiModelProperty("问卷设计id")
    @TableField("DESIGN_ID")
    private String designId;

    @ApiModelProperty("填写内容")
    @TableField("CONTENT")
    private String content;
}
