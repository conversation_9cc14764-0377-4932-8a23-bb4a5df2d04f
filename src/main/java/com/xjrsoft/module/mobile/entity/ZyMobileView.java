package com.xjrsoft.module.mobile.entity;

import cn.zhxu.bs.bean.DbIgnore;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xjrsoft.module.zy.form.pojo.entity.ZyFormSchemeinfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-01
 */
@Getter
@Setter
@TableName("ZY_MOBILE_VIEW")
@ApiModel(value = "ZyMobileView对象", description = "")
public class ZyMobileView implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private String id;

    @TableField("SCHEME")
    private String scheme;

    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_DATE")
    private LocalDateTime updateDate;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("REMARKS")
    private String remarks;

    @TableField("DEL_FLAG")
    private String delFlag;

    @TableField("NAME")
    private String name;

    @TableField("SCHEMEINFO_ID")
    private String schemeinfoId;

    @TableField("IS_USED")
    private String isUsed;

    @TableField("IS_TODO_VIEW")
    private String isTodoView;

    @ApiModelProperty("扩展参数")
    @TableField("EXT_PARAM")
    private String extParam;

    @ApiModelProperty("对应主菜单Id")
    @TableField("BASIC_ID")
    private String basicId;

    @ApiModelProperty("是否为调查问卷视图 0否 1是")
    @TableField("IS_SURVEY")
    private String isSurvey;

    @ApiModelProperty("调查问卷视图列表类型")
    @TableField("SURVEY_TYPE")
    private String surveyType;

    @ApiModelProperty("FORM_TEMPLATE_ID")
    @TableField("FORM_TEMPLATE_ID")
    private String formTemplateId;

    @TableField(exist = false)
    @DbIgnore
    private ZyFormSchemeinfo schemeInfo;

    @ApiModelProperty("GL_PC_VIEW_ID")
    @TableField("GL_PC_VIEW_ID")
    private String glPcViewId;



}
