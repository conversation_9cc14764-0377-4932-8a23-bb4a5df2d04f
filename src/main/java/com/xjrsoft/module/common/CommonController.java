package com.xjrsoft.module.common;

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.xjrsoft.common.pojo.CommonResult;
import com.xjrsoft.common.utils.DatasourceUtil;
import com.xjrsoft.module.common.vo.CopyItemVO;
import com.xjrsoft.module.common.vo.CopyVO;
import com.xjrsoft.module.system.service.ICodeRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.sql.SQLException;
import java.util.List;
import java.util.Set;

import static com.xjrsoft.common.pojo.CommonResult.error;
import static com.xjrsoft.common.pojo.CommonResult.success;

/**
 * @ClassName CommonController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/3 19:51
 * @Version 1.0
 */
@Api(tags = "通用接口")
@Log4j2
@RestController
@RequestMapping("/XJRSOFT.CommonController")
public class CommonController {

    @Autowired
    ICodeRuleService codeRuleService;
    /**
     * 复制
     *
     * @param copyVO
     * @return
     */
    @ApiOperation("复制")
    @PostMapping("/copy")
    public CommonResult<List<String>> copy(@Valid @RequestBody CopyVO copyVO) {
        List<String> ids = new java.util.ArrayList<>();
        try {
            Entity entity = Db.use(DatasourceUtil.getDataSource("master")).get(copyVO.getTableName(), "id", copyVO.getId());
            List<CopyItemVO> items = copyVO.getItems();
            Set<String> fieldNames = entity.getFieldNames();
            for (CopyItemVO item : items) {
                int copyNum = item.getCopyNum();
                for (int i = 0; i < copyNum; i++) {
                    Entity copyEntity = Entity.create(copyVO.getTableName());
                    for (String fieldName : fieldNames) {
                        if (fieldName.equalsIgnoreCase("id") ||
                                fieldName.equalsIgnoreCase("ROWNUM_")) {
                            continue;
                        }
                        if (copyVO.getTableName().equalsIgnoreCase("ZY_FA_CARD")) {
                            if (fieldName.equalsIgnoreCase("GDDM")) {
                                String code = codeRuleService.genEncode("ZC");
                                copyEntity.set(fieldName, code);
                                continue;
                            }
                        }
                        copyEntity.set(fieldName, entity.get(fieldName));
                    }
                    item.getFormData().forEach(itemFormData -> {
                        if (itemFormData.getKey().equalsIgnoreCase("id") ||
                                itemFormData.getKey().equalsIgnoreCase("ROWNUM_")||
                                itemFormData.getKey().equalsIgnoreCase("GDDM")
                        ) {
                            return;
                        }
                        copyEntity.set(itemFormData.getKey(), itemFormData.getValue());
                    });
                    long snowflakeNextId = IdUtil.getSnowflakeNextId();
                    copyEntity.set("id", snowflakeNextId);
                    ids.add(snowflakeNextId + "");
                    try {
                        Db.use(DatasourceUtil.getDataSource("master")).insert(copyEntity);
                    } catch (SQLException e) {
                        log.error("复制失败", e);
                        return error(500, e.getMessage());
                    }
                }
            }

        } catch (SQLException e) {
            return error(500, e.getMessage());

        }
        return success(ids);
    }

}
