package com.xjrsoft.module.zy.form.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Entity;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xjrsoft.common.exception.MyException;
import com.xjrsoft.common.model.result.R;
import com.xjrsoft.common.page.PageOutput;
import com.xjrsoft.common.pojo.CommonResult;
import com.xjrsoft.common.utils.JSQLParserUtil;
import com.xjrsoft.common.utils.UploadUtil;
import com.xjrsoft.common.utils.UserUtil;
import com.xjrsoft.module.organization.entity.Department;
import com.xjrsoft.module.organization.service.IDepartmentService;
import com.xjrsoft.module.organization.service.IUserService;
import com.xjrsoft.module.system.service.IFileService;
import com.xjrsoft.module.workflow.entity.WorkflowApproveRecord;
import com.xjrsoft.module.workflow.service.IWorkflowApproveRecordService;
import com.xjrsoft.module.zy.form.base.BaseController;
import com.xjrsoft.module.zy.form.pojo.entity.SysBillPrintDs;
import com.xjrsoft.module.zy.form.pojo.entity.SysBillPrintDsDetail;
import com.xjrsoft.module.zy.form.pojo.entity.SysPrintTemplate;
import com.xjrsoft.module.zy.form.pojo.entity.SysTemplate;
import com.xjrsoft.module.zy.form.pojo.vo.AddImagesAtCoordinates;
import com.xjrsoft.module.zy.form.pojo.vo.ConfirmStampPdfVO;
import com.xjrsoft.module.zy.form.pojo.vo.StampPdfVO;
import com.xjrsoft.module.zy.form.pojo.vo.ToStampPdfVO;
import com.xjrsoft.module.zy.form.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 打印模版-文件管理
 *
 * <AUTHOR>
 * @since 2024-09-08
 */
@Api(tags = "打印模版-文件管理")
@RestController
@Slf4j
@RequestMapping("/sysTemplate")
public class SysTemplateController extends BaseController<SysTemplate, SysTemplate, SysTemplate, ISysTemplateService> {
    @Autowired
    public ISysTemplateService iSysTemplateService;
    @Autowired
    public SysPrintTemplateService sysPrintTemplateService;

    @Autowired
    public SysBillPrintDsService sysBillPrintDsService;
    @Autowired
    public SysBillPrintDsDetailService sysBillPrintDsDetailService;
    @Autowired
    public ZyFormDatasourceService zyFormDatasourceService;

    @Autowired
    public IFileService iFileService;

    @Autowired
    public IWorkflowApproveRecordService approveRecordService;
    @Autowired
    public IUserService userService;

    @Autowired
    public IDepartmentService iDepartmentService;

    @Autowired
    private Executor asyncPoolTaskExecutor;

    @Value("${pdf.url:}")
    private String pdfUrl;
    @Value("${pdf.searchKeyword:}")
    private String searchKeywordUrl;
    @Value("${pdf.stampPdf:}")
    private String stampPdfUrl;

    @PostMapping
    @ApiOperation(value = "上传模版文件")
    public CommonResult<SysTemplate> uploadFile(@RequestParam(value = "file", required = true) MultipartFile multipartFile, @RequestParam(required = true) String reportId) throws Exception {
        if (multipartFile.isEmpty()) {
            return CommonResult.error("文件不能为空");
        }
        String filePath = UploadUtil.uploadFile(multipartFile);

        iSysTemplateService.remove(new LambdaQueryWrapper<SysTemplate>()
                .eq(SysTemplate::getReportId, reportId));
        SysTemplate sysTemplate = new SysTemplate();
        sysTemplate.setContent(new String(multipartFile.getBytes(), StandardCharsets.UTF_8));
        sysTemplate.setReportId(reportId);
        sysTemplate.setName(multipartFile.getOriginalFilename());
        sysTemplate.setContent(filePath);
        iSysTemplateService.save(sysTemplate);
        return CommonResult.success(sysTemplate);
    }

    @GetMapping("/export")
    @ApiOperation(value = "下载打印模版")
    public ResponseEntity<byte[]> export(@RequestParam(required = true) String reportId, HttpServletResponse response) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        SysTemplate sysTemplate = iSysTemplateService.getOne(new LambdaQueryWrapper<SysTemplate>().eq(SysTemplate::getReportId, reportId));
        String fileName = sysTemplate.getName().replace(" ", "");

        try {
            InputStream inputStream = UploadUtil.download(sysTemplate.getContent());
            int bytesRead;
            byte[] buffer = new byte[1024];
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            // 关闭输入流
            inputStream.close();
            fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        // 设置响应头
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + fileName);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);

        // 返回文件流
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + fileName)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(baos.toByteArray());
    }

    @GetMapping("/yl")
    @ApiOperation(value = "预览pdf")
    public ResponseEntity<byte[]> yl(@RequestParam(required = true) String pageId,
                                     @RequestParam(required = true) String ywid,
                                     @RequestParam(required = false) String processId,
                                     @RequestParam(required = true) boolean multi,
                                     @RequestParam(required = false) String hjKey,
                                     @RequestParam(required = false) boolean ygms,
                                     @RequestParam(required = false) Integer printPageSize,
                                     @RequestParam(required = false) boolean isWord,
                                     @RequestParam(required = false) boolean isData, HttpServletResponse response) throws Exception {
        ByteArrayOutputStream pdfOutput = new ByteArrayOutputStream();
        //表单模版
        SysPrintTemplate sysPrintTemplate = sysPrintTemplateService.getOne(new LambdaQueryWrapper<SysPrintTemplate>()
                .eq(SysPrintTemplate::getPageId, pageId));

        if (sysPrintTemplate == null) {
            throw new MyException("未找到打印模版");
        }
        //模版文件
        SysTemplate template = iSysTemplateService.getOne(new LambdaQueryWrapper<SysTemplate>()
                .eq(SysTemplate::getReportId, sysPrintTemplate.getId()));
        if (template == null) {
            throw new MyException("未找到打印模版文件");
        }

        String fileName = template.getName().replace(" ", "");
        InputStream inputStream = UploadUtil.download(template.getContent());
        File tempFile = File.createTempFile(fileName, "docx");
        String billNo = null;

        try {
            try (FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            SysBillPrintDs sysBillPrintDs = sysBillPrintDsService.getOne(new LambdaQueryWrapper<SysBillPrintDs>()
                    .eq(SysBillPrintDs::getPageId, pageId));
            if (sysBillPrintDs == null) {
                sysBillPrintDs = sysBillPrintDsService.getOne(new LambdaQueryWrapper<SysBillPrintDs>()
                        .eq(SysBillPrintDs::getPageId, sysPrintTemplate.getDatasetId()));
            }
            String primaryTable = sysBillPrintDs.getPrimaryTable();
            Map<String, Object> dataMap = new HashMap<>();
            List<String> childDataList = new ArrayList<>();

            if (!multi) {
                String[] strings = ywid.split(",");
                List mainList = Collections.synchronizedList(new ArrayList<>());
                Map<String, Object> asyncDataBits = new ConcurrentHashMap<>();
                AtomicReference<String> billNoRef = new AtomicReference<>(null);
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                for (String string : strings) {
                    SysBillPrintDs finalSysBillPrintDs = sysBillPrintDs;
                    CompletableFuture<Void> f = CompletableFuture.runAsync(() -> {
                        String sql = String.format("select * from %s where %s = '%s'", primaryTable, finalSysBillPrintDs.getPrimaryField(), string);
                        asyncDataBits.put(finalSysBillPrintDs.getAliasName() + string + "sql", sql);
                        R r = zyFormDatasourceService.executeSql(new HashMap<>(), sql, null, null, false, null);

                        if (r.getData() == null) {
                            throw new MyException("数据源异常" + sql);
                        }
                        PageOutput pageOutput = (PageOutput) r.getData();
                        List list = pageOutput.getList();
                        if (list == null || list.size() == 0) {
                            throw new MyException("数据为空" + pageOutput.getSelectSql());
                        }
                        Object o = ((Map) list.get(0)).get("BILL_NO");
                        if (o == null) {
                            ((Map) list.get(0)).get("BLII_ID");
                        }
                        if (o != null) {
                            billNoRef.compareAndSet(null, o.toString());
                        }
                        synchronized (mainList) {
                            mainList.addAll(list);
                        }
                    }, asyncPoolTaskExecutor);
                    futures.add(f);
                }
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                dataMap.putAll(asyncDataBits);
                if (billNo == null) {
                    billNo = billNoRef.get();
                }

                if (StringUtils.isNotEmpty(hjKey)) {
                    for (String s : hjKey.split(",")) {
                        BigDecimal sum = new BigDecimal(0);
                        for (Object o : mainList) {
                            try {

                                Object hj = BeanUtil.getFieldValue(o, s);
                                sum = sum.add(new BigDecimal(hj.toString().replace(",", "")));
                            } catch (Exception e) {
                                log.error(e.getMessage());
                            }
                        }
                        dataMap.put(s + "Sum", sum);
                    }

                }
                childDataList.add(sysBillPrintDs.getAliasName());
                dataMap.put(sysBillPrintDs.getAliasName(), mainList);
                if (!mainList.isEmpty()) {
                    dataMap.put(sysBillPrintDs.getAliasName() + "Obj", mainList.get(0));
                }
            } else {
                String sql = String.format("select * from %s where %s = '%s'", primaryTable, sysBillPrintDs.getPrimaryField(), ywid);
                dataMap.put(sysBillPrintDs.getAliasName() + "sql", sql);
                R r = zyFormDatasourceService.executeSql(new HashMap<>(), sql, null, null, false, null);

                if (r.getData() == null) {
                    throw new MyException("数据源异常" + sql);
                }
                PageOutput pageOutput = (PageOutput) r.getData();
                List list = pageOutput.getList();

                if (list == null || list.size() == 0) {
                    throw new MyException("数据为空" + pageOutput.getSelectSql());
                }
                Entity mainEntity = Entity.parse(list.get(0));
                dataMap.put(sysBillPrintDs.getAliasName(), list.get(0));
                List<SysBillPrintDsDetail> childrenList = sysBillPrintDsDetailService.list(new LambdaQueryWrapper<SysBillPrintDsDetail>()
                        .eq(SysBillPrintDsDetail::getBillPrintId, sysBillPrintDs.getId()));

                Map<String, Object> asyncChildDataBits = new ConcurrentHashMap<>();
                List<String> asyncChildDataList = Collections.synchronizedList(new ArrayList<>());
                List<CompletableFuture<Void>> childFutures = new ArrayList<>();

                for (SysBillPrintDsDetail sysBillPrintDsDetail : childrenList) {
                    CompletableFuture<Void> childFuture = CompletableFuture.runAsync(() -> {
                        String childSql = String.format("select * from %s where %s = '%s'", sysBillPrintDsDetail.getForeignTable(), sysBillPrintDsDetail.getForeignField(), mainEntity.get(sysBillPrintDsDetail.getPrimaryField()));
                        asyncChildDataBits.put(sysBillPrintDsDetail.getAliasName() + "sql", childSql);
                        R childR = zyFormDatasourceService.executeSql(new HashMap<>(), childSql, null, null, false, null);
                        if (childR.getData() != null) {
                            PageOutput pageOutputChild = (PageOutput) childR.getData();
                            List pageOutputChildList = pageOutputChild.getList();
                            if (pageOutputChildList != null && pageOutputChildList.size() > 0) {
                                asyncChildDataBits.put(sysBillPrintDsDetail.getAliasName(), pageOutputChildList);
                                asyncChildDataList.add(sysBillPrintDsDetail.getAliasName());
                                if (StringUtils.isNotEmpty(hjKey)) {
                                    for (String s : hjKey.split(",")) {
                                        float sum = 0;
                                        for (Object o : pageOutputChildList) {
                                            try {
                                                Object hj = BeanUtil.getFieldValue(o, hjKey);
                                                sum += Float.parseFloat(hj.toString());
                                            } catch (Exception e) {
                                                log.error(e.getMessage());
                                            }
                                        }
                                        asyncChildDataBits.put(s + "Sum", sum);
                                    }
                                }

                            } else {
                                asyncChildDataBits.put(sysBillPrintDsDetail.getAliasName(), new ArrayList<>());
                                asyncChildDataList.add(sysBillPrintDsDetail.getAliasName());
                            }
                            asyncChildDataBits.put(sysBillPrintDsDetail.getAliasName() + "PageSize", sysBillPrintDsDetail.getRecordSize());
                        } else {
                            asyncChildDataBits.put(sysBillPrintDsDetail.getAliasName(), new ArrayList<>());
                            asyncChildDataList.add(sysBillPrintDsDetail.getAliasName());
                            asyncChildDataBits.put(sysBillPrintDsDetail.getAliasName() + "PageSize", sysBillPrintDsDetail.getRecordSize());
                        }
                    }, asyncPoolTaskExecutor);
                    childFutures.add(childFuture);
                }

                CompletableFuture.allOf(childFutures.toArray(new CompletableFuture[0])).join();
                dataMap.putAll(asyncChildDataBits);
                childDataList.addAll(asyncChildDataList);

            }
            Map<String, WorkflowApproveRecord> spxx = new HashMap<>();
            if (StringUtils.isNotEmpty(processId)) {
                List<WorkflowApproveRecord> workflowApproveRecordList = approveRecordService.list(new LambdaQueryWrapper<WorkflowApproveRecord>()
                        .eq(WorkflowApproveRecord::getProcessId, processId)
                        .orderByAsc(WorkflowApproveRecord::getApproveTime));
                for (WorkflowApproveRecord workflowApproveRecord : workflowApproveRecordList) {
                    String upperCase = PinyinUtil.getFirstLetter(workflowApproveRecord.getTaskName(), "").toUpperCase();
                    try {
                        workflowApproveRecord.setApproveUserName(userService.getById(workflowApproveRecord.getApproveUserId()).getName());
                    } catch (Exception e) {

                    }
                    spxx.put(upperCase.replace("（", "").replace("）", "").replace("(", "").replace(")", ""), workflowApproveRecord);
                }
            }
            dataMap.put("ygms", ygms);
            dataMap.put("childDataList", childDataList);
            dataMap.put("user", UserUtil.getGzhjV2());
            dataMap.put("now", DateUtil.now());
            dataMap.put("spxx", spxx);
            if (isData) {
                response.getOutputStream().write(JSON.toJSONString(dataMap).getBytes("UTF-8"));
                response.getOutputStream().flush();
                response.getOutputStream().close();
                return null;
            }
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("file", tempFile);
            //创建Document对象
            JSONConfig jsonConfig = JSONConfig.create()
                    .setIgnoreNullValue(false);
            dataMap.put("name", null);
            dataMap.put("printPageSize", printPageSize);
            dataMap.put("isWord", isWord);
            SerializeConfig config = new SerializeConfig();
            // 关闭循环引用检测
            config.setAsmEnable(false);
            // 创建一个 ValueFilter 实现类
            ValueFilter filter = new ValueFilter() {
                @Override
                public Object process(Object object, String name, Object value) {
                    if (value == null) {
                        return "";
                    }
                    return value;
                }
            };
            paramMap.put("json", JSON.toJSONString(dataMap, config, filter,
                    SerializerFeature.WriteMapNullValue,
                    SerializerFeature.WriteNullListAsEmpty,
                    SerializerFeature.WriteDateUseDateFormat,
                    SerializerFeature.WriteNullStringAsEmpty,
                    SerializerFeature.DisableCircularReferenceDetect
            ));
            HttpRequest httpRequest = HttpUtil.createPost(pdfUrl);
            httpRequest.form(paramMap);
            InputStream bodyStream = httpRequest.execute().bodyStream();
            IoUtil.copy(bodyStream, pdfOutput);
            if (bodyStream != null) {
                try {
                    bodyStream.close();
                } catch (Exception e) {

                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 2. 设置HTTP响应头
        HttpHeaders headers = new HttpHeaders();
        String fileNameUUID = "";
        if (isWord) {
            if (StringUtils.isNotEmpty(billNo)) {
                fileName = fileName.replaceAll(".docx", "") + billNo + ".docx";
            }
            fileNameUUID = fileName;
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        } else {
            fileNameUUID = IdUtil.simpleUUID() + ".pdf";
            headers.setContentType(MediaType.APPLICATION_PDF);
        }
        //乱码
        String encodedFilename = URLEncoder.encode(fileNameUUID, "UTF-8");
        headers.set(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + encodedFilename);

        // 3. 返回ResponseEntity对象
        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfOutput.toByteArray());

    }

    /**
     * 获取数据集字段
     */
    @ApiOperation("获取数据集字段")
    @GetMapping("/getQueryFields")
    public CommonResult<List<String>> getQueryFields(String tableName) {
        String sql = "select * from " + tableName;
        return CommonResult.success(JSQLParserUtil.getSelectColumn(sql));
    }

    @PostMapping("/stampPdf")
    @ApiOperation(value = "pdf 盖章")
    public CommonResult<com.xjrsoft.module.system.entity.File> stampPdf(@RequestBody ToStampPdfVO toStampPdfVO) {

        if (toStampPdfVO.getMuilt() == null) {
            toStampPdfVO.setMuilt(false);
        }
        if (StringUtils.isEmpty(toStampPdfVO.getKeyWord())) {
            throw new MyException("盖章关键字不能为空");
        }
        if (StringUtils.isEmpty(toStampPdfVO.getFileId())) {
            throw new MyException("PDF文件不能为空");
        }
        if (StringUtils.isEmpty(toStampPdfVO.getDepartmentId())) {
            throw new MyException("盖章单位不能为空");
        }
        com.xjrsoft.module.system.entity.File pdfFile = iFileService.getById(toStampPdfVO.getFileId());
        if (pdfFile == null) {
            throw new MyException("文件不存在");
        }

        Department department = iDepartmentService.getById(toStampPdfVO.getDepartmentId());
        if (department == null) {
            throw new MyException("盖章单位不存在");
        }
        if (StringUtils.isEmpty(department.getGzUrl())) {
            throw new MyException("未上传公章");
        }

        String fileName = pdfFile.getFileName();
        InputStream inputStream = UploadUtil.download(pdfFile.getFileUrl());
        File tempFile = null;
        try {
            tempFile = File.createTempFile(fileName, "pdf");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        try (FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("pdfFile", tempFile);
        paramMap.put("keyWord", toStampPdfVO.getKeyWord());
        HttpRequest httpRequest = HttpUtil.createPost(searchKeywordUrl);
        httpRequest.form(paramMap);
        String body = httpRequest.execute().body();
        StampPdfVO stampPdfVO = JSON.parseObject(body, StampPdfVO.class);
        if (!stampPdfVO.isFound()) {
            throw new MyException("未找到关键字");
        }

        if (!stampPdfVO.isFound()) {
            throw new MyException("未找到关键字");
        }
        if (stampPdfVO.getMatchCount() > 0 && !toStampPdfVO.getMuilt()) {
            throw new MyException("存在多个位置匹配，是否继续？");
        }
        paramMap.put("yzUrl", department.getGzUrl());
        HttpRequest stampPdfRequest = HttpUtil.createPost(stampPdfUrl);
        stampPdfRequest.form(paramMap);
        ByteArrayOutputStream pdfOutput = new ByteArrayOutputStream();
        InputStream bodyStream = stampPdfRequest.execute().bodyStream();

        String minioUrl = null;
        if (bodyStream != null) {
            try {
                // 将bodyStream复制到ByteArrayOutputStream中
                IoUtil.copy(bodyStream, pdfOutput);

                // 上传到MinIO
                byte[] pdfData = pdfOutput.toByteArray();
                minioUrl = UploadUtil.uploadFile(pdfData, ".pdf");

                log.info("PDF文件已成功上传到MinIO，URL: {}", minioUrl);

                bodyStream.close();
            } catch (Exception e) {
                log.error("上传PDF到MinIO失败", e);
                throw new MyException("上传PDF文件失败: " + e.getMessage());
            }
        }

        // 创建文件记录并返回
        com.xjrsoft.module.system.entity.File stampedFile = new com.xjrsoft.module.system.entity.File();
        stampedFile.setFileName(fileName + "_stamped.pdf");
        stampedFile.setFileUrl(minioUrl);
        stampedFile.setFileSize((long) pdfOutput.size());
        stampedFile.setFileSuffiex(".pdf");
        stampedFile.setFileType("pdf");

        return CommonResult.success(stampedFile);

    }

    @PostMapping("/confirmStampPdf")
    @ApiOperation(value = "确认盖章")
    public CommonResult<String> confirmStampPdf(@RequestBody ConfirmStampPdfVO confirmStampPdfVO) {
        com.xjrsoft.module.system.entity.File file = iFileService.getById(confirmStampPdfVO.getFileId());
        file.setGzFileUrl(confirmStampPdfVO.getFileUrl());
        file.setYgz("1");
        iFileService.updateById(file);
        return CommonResult.success("操作成功");

    }

    @PostMapping("/addImagesAtCoordinates")
    @ApiOperation(value = "pdf 盖章")
    public CommonResult<com.xjrsoft.module.system.entity.File> addImagesAtCoordinates(@RequestBody AddImagesAtCoordinates addImagesAtCoordinates) {


        com.xjrsoft.module.system.entity.File pdfFile = iFileService.getById(addImagesAtCoordinates.getFileId());
        if (pdfFile == null) {
            throw new MyException("文件不存在");
        }
        addImagesAtCoordinates.setUrl(pdfFile.getFileUrl());
        HttpRequest stampPdfRequest = HttpUtil.createPost(stampPdfUrl);
        stampPdfRequest.body(JSON.toJSONString(addImagesAtCoordinates));
        ByteArrayOutputStream pdfOutput = new ByteArrayOutputStream();
        InputStream bodyStream = stampPdfRequest.execute().bodyStream();
        String minioUrl = null;
        if (bodyStream != null) {
            try {
                // 将bodyStream复制到ByteArrayOutputStream中
                IoUtil.copy(bodyStream, pdfOutput);

                // 上传到MinIO
                byte[] pdfData = pdfOutput.toByteArray();
                minioUrl = UploadUtil.uploadFile(pdfData, ".pdf");

                log.info("PDF文件已成功上传到MinIO，URL: {}", minioUrl);

                bodyStream.close();
            } catch (Exception e) {
                log.error("上传PDF到MinIO失败", e);
                throw new MyException("上传PDF文件失败: " + e.getMessage());
            }
        }

        // 创建文件记录并返回
        com.xjrsoft.module.system.entity.File stampedFile = new com.xjrsoft.module.system.entity.File();
        stampedFile.setFileName(pdfFile.getFileName() + "_stamped.pdf");
        stampedFile.setFileUrl(pdfFile.getFileUrl());
        stampedFile.setFileSize((long) pdfOutput.size());
        stampedFile.setFileSuffiex(".pdf");
        stampedFile.setFileType("pdf");
        stampedFile.setYgz("1");
        stampedFile.setGzFileUrl(minioUrl);

        //修改数据库
        pdfFile.setGzFileUrl(minioUrl);
        pdfFile.setYgz("1");
        iFileService.updateById(pdfFile);

        return CommonResult.success(stampedFile);

    }
}
