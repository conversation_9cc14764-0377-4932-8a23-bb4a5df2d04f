package com.xjrsoft.module.print.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: tzx
 * @Date: 2023/5/19 10:28
 */
@Data
public class UpdatePrintSchemaDto {
    @ApiModelProperty("id")
    @NotNull(message = "id不能为空！")
    private Long id;

    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空！")
    private String name;

    @ApiModelProperty("编码")
    @NotBlank(message = "编码不能为空！")
    private String code;

    @ApiModelProperty("分类id")
    @NotNull(message = "分类不能为空！")
    private Long category;

    @ApiModelProperty("api配置")
    private String apiConfig;

    @ApiModelProperty("是否生成菜单")
    private Integer isMenu;

    @ApiModelProperty("菜单id")
    private String menuId;

    @ApiModelProperty("父级菜单")
    private String parentId;

    @ApiModelProperty("菜单名称")
    private String menuName;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("打印配置")
    private String content;

    @ApiModelProperty("排序")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;
}
